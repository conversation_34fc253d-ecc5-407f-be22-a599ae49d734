FROM python:3.10-slim

WORKDIR /app

RUN apt-get update && apt-get install -y \
    fonts-ebgaramond \
    ffmpeg \
    libsndfile1 \
    fonts-dejavu \
    build-essential \
    g++ \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Download and install Nunito font manually from GitHub
RUN mkdir -p /usr/share/fonts/truetype/nunito && \
    wget -O /tmp/nunito.zip "https://github.com/googlefonts/nunito/archive/refs/heads/main.zip" && \
    unzip /tmp/nunito.zip -d /tmp/ && \
    find /tmp/nunito-main -name "*.ttf" -exec cp {} /usr/share/fonts/truetype/nunito/ \; && \
    rm -rf /tmp/nunito.zip /tmp/nunito-main && \
    fc-cache -fv

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY api_server /app/api_server
COPY utils /app/utils
COPY video /app/video
COPY server.py /app/server.py

ENV PYTHONUNBUFFERED=1

CMD ["fastapi", "run", "server.py", "--host", "0.0.0.0", "--port", "8000"]
